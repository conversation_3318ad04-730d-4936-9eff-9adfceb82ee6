#!/usr/bin/env python3
"""
Fast ImageJ Stitching with Phase Correlation Position Correction
Optimized for speed and accuracy
"""

import cv2
import numpy as np
import os
import argparse
from typing import List, Tuple, Optional
import logging
from dataclasses import dataclass
import time
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TileInfo:
    """Information about each image tile"""
    filename: str
    position: Tuple[float, float]
    corrected_position: Optional[Tuple[float, float]] = None
    image: Optional[np.ndarray] = None
    size: Optional[Tuple[int, int]] = None

class FastStitcher:
    """
    Fast stitching with phase correlation and optimized fusion
    """
    
    def __init__(self, 
                 fusion_method: str = "linear_blending",
                 enable_position_correction: bool = True,
                 num_threads: int = None):
        """
        Initialize fast stitcher
        
        Args:
            fusion_method: Fusion method
            enable_position_correction: Enable position correction
            num_threads: Number of threads for parallel processing
        """
        self.fusion_method = fusion_method
        self.enable_position_correction = enable_position_correction
        self.num_threads = num_threads or min(8, multiprocessing.cpu_count())
        self.tiles: List[TileInfo] = []
    
    def parse_tile_configuration(self, config_path: str, image_directory: str = None) -> None:
        """Parse TileConfiguration.txt file"""
        if image_directory is None:
            image_directory = os.path.dirname(config_path)
        
        self.tiles = []
        
        with open(config_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#') or line.startswith('dim'):
                    continue
                
                if ';' in line:
                    parts = line.split(';')
                    if len(parts) >= 3:
                        filename = parts[0].strip()
                        position_str = parts[2].strip()
                        
                        # Extract coordinates
                        import re
                        coord_match = re.search(r'\(([-+]?\d*\.?\d+),\s*([-+]?\d*\.?\d+)\)', position_str)
                        if coord_match:
                            x = float(coord_match.group(1))
                            y = float(coord_match.group(2))
                            
                            full_path = os.path.join(image_directory, filename)
                            tile = TileInfo(filename=full_path, position=(x, y))
                            self.tiles.append(tile)
        
        logger.info(f"Parsed {len(self.tiles)} tiles from configuration")
    
    def load_images_parallel(self) -> None:
        """Load images in parallel"""
        logger.info("Loading images in parallel...")
        start_time = time.time()
        
        def load_single_image(tile: TileInfo) -> TileInfo:
            img = cv2.imread(tile.filename, cv2.IMREAD_GRAYSCALE)
            if img is not None:
                tile.image = img
                tile.size = (img.shape[1], img.shape[0])
            return tile
        
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            self.tiles = list(executor.map(load_single_image, self.tiles))
        
        # Filter out failed loads
        self.tiles = [tile for tile in self.tiles if tile.image is not None]
        
        load_time = time.time() - start_time
        logger.info(f"Loaded {len(self.tiles)} images in {load_time:.2f}s")
    
    def phase_correlation_correction(self, img1: np.ndarray, img2: np.ndarray, 
                                   overlap_region: Tuple[int, int, int, int]) -> Optional[Tuple[float, float]]:
        """
        Fast phase correlation for position correction
        
        Args:
            img1, img2: Input images
            overlap_region: (x1, y1, x2, y2) overlap region in img1 coordinates
            
        Returns:
            (dx, dy) correction or None if failed
        """
        try:
            x1, y1, x2, y2 = overlap_region
            
            # Extract overlap regions
            region1 = img1[y1:y2, x1:x2]
            
            # Calculate corresponding region in img2
            h, w = region1.shape
            if h < 64 or w < 64:  # Too small for reliable correlation
                return None
            
            # Resize for speed if too large
            if h > 512 or w > 512:
                scale = min(512/h, 512/w)
                new_h, new_w = int(h * scale), int(w * scale)
                region1 = cv2.resize(region1, (new_w, new_h))
                h, w = new_h, new_w
            
            # Try to extract corresponding region from img2
            # This is a simplified approach - in practice you'd need more sophisticated overlap detection
            region2 = img2[:h, :w] if img2.shape[0] >= h and img2.shape[1] >= w else None
            
            if region2 is None:
                return None
            
            # Ensure same size
            min_h, min_w = min(region1.shape[0], region2.shape[0]), min(region1.shape[1], region2.shape[1])
            region1 = region1[:min_h, :min_w]
            region2 = region2[:min_h, :min_w]
            
            # Phase correlation
            f1 = np.fft.fft2(region1.astype(np.float32))
            f2 = np.fft.fft2(region2.astype(np.float32))
            
            # Cross power spectrum
            cross_power = f1 * np.conj(f2)
            cross_power_norm = cross_power / (np.abs(cross_power) + 1e-10)
            
            # Inverse FFT
            correlation = np.real(np.fft.ifft2(cross_power_norm))
            
            # Find peak
            peak_y, peak_x = np.unravel_index(np.argmax(correlation), correlation.shape)
            
            # Convert to shift
            if peak_x > min_w // 2:
                peak_x -= min_w
            if peak_y > min_h // 2:
                peak_y -= min_h
            
            # Get correlation value
            corr_value = correlation[peak_y % min_h, peak_x % min_w]
            
            # Validate correlation
            if corr_value < 0.3 or abs(peak_x) > 100 or abs(peak_y) > 100:
                return None
            
            return (float(peak_x), float(peak_y))
            
        except Exception as e:
            logger.debug(f"Phase correlation failed: {e}")
            return None
    
    def correct_positions_fast(self) -> None:
        """Fast position correction using simplified overlap detection"""
        if not self.enable_position_correction:
            for tile in self.tiles:
                tile.corrected_position = tile.position
            return
        
        logger.info("Performing fast position correction...")
        start_time = time.time()
        
        # Initialize with original positions
        for tile in self.tiles:
            tile.corrected_position = tile.position
        
        corrections_made = 0
        
        # Simple grid-based correction for adjacent tiles
        # This assumes a regular grid pattern
        for i, tile in enumerate(self.tiles):
            if i == 0:  # Skip first tile (reference)
                continue
            
            # Find the most likely previous tile (closest in position)
            prev_tile = self.tiles[i-1]
            
            # Simple overlap region estimation
            pos1, pos2 = prev_tile.corrected_position, tile.position
            size1, size2 = prev_tile.size, tile.size
            
            # Estimate overlap region (simplified)
            overlap_x = max(0, min(pos1[0] + size1[0], pos2[0] + size2[0]) - max(pos1[0], pos2[0]))
            overlap_y = max(0, min(pos1[1] + size1[1], pos2[1] + size2[1]) - max(pos1[1], pos2[1]))
            
            if overlap_x > 50 and overlap_y > 50:  # Sufficient overlap
                # Define overlap region in first image
                overlap_region = (
                    int(max(0, pos2[0] - pos1[0])),
                    int(max(0, pos2[1] - pos1[1])),
                    int(min(size1[0], pos2[0] - pos1[0] + overlap_x)),
                    int(min(size1[1], pos2[1] - pos1[1] + overlap_y))
                )
                
                correction = self.phase_correlation_correction(
                    prev_tile.image, tile.image, overlap_region
                )
                
                if correction is not None:
                    old_pos = tile.corrected_position
                    new_pos = (old_pos[0] + correction[0], old_pos[1] + correction[1])
                    tile.corrected_position = new_pos
                    corrections_made += 1
        
        correction_time = time.time() - start_time
        logger.info(f"Position correction completed in {correction_time:.2f}s, "
                   f"made {corrections_made} corrections")
    
    def ultra_fast_fusion(self, canvas: np.ndarray, weights: np.ndarray, 
                         img: np.ndarray, x_offset: int, y_offset: int) -> None:
        """Ultra-fast fusion using optimized NumPy operations"""
        h, w = img.shape
        canvas_h, canvas_w = canvas.shape
        
        # Calculate valid region with bounds checking
        y_start = max(0, y_offset)
        y_end = min(canvas_h, y_offset + h)
        x_start = max(0, x_offset)
        x_end = min(canvas_w, x_offset + w)
        
        if y_start >= y_end or x_start >= x_end:
            return
        
        # Calculate source region
        src_y_start = y_start - y_offset
        src_y_end = y_end - y_offset
        src_x_start = x_start - x_offset
        src_x_end = x_end - x_offset
        
        # Extract regions
        canvas_region = canvas[y_start:y_end, x_start:x_end]
        weight_region = weights[y_start:y_end, x_start:x_end]
        img_region = img[src_y_start:src_y_end, src_x_start:src_x_end].astype(np.float32)
        
        if self.fusion_method == "linear_blending":
            # Fast distance transform approximation
            region_h, region_w = img_region.shape
            
            # Create simple distance weights (faster than full distance transform)
            y_dist = np.minimum(np.arange(region_h), np.arange(region_h)[::-1]) + 1
            x_dist = np.minimum(np.arange(region_w), np.arange(region_w)[::-1]) + 1
            
            # Broadcast to create 2D weight map
            blend_weights = np.minimum(y_dist[:, None], x_dist[None, :]).astype(np.float32)
            blend_weights /= np.max(blend_weights)  # Normalize
            
            # Vectorized blending
            first_time = weight_region == 0
            
            # First time pixels
            canvas_region[first_time] = img_region[first_time]
            weight_region[first_time] = blend_weights[first_time]
            
            # Overlapping pixels
            overlap = ~first_time
            if np.any(overlap):
                total_weight = weight_region[overlap] + blend_weights[overlap]
                canvas_region[overlap] = (
                    (canvas_region[overlap] * weight_region[overlap] + 
                     img_region[overlap] * blend_weights[overlap]) / total_weight
                )
                weight_region[overlap] = total_weight
                
        elif self.fusion_method == "max":
            # Simple max fusion
            np.maximum(canvas_region, img_region, out=canvas_region)
    
    def stitch_images_fast(self) -> np.ndarray:
        """Ultra-fast stitching pipeline"""
        logger.info(f"Starting ultra-fast stitching of {len(self.tiles)} tiles")
        start_time = time.time()
        
        # Load images in parallel
        self.load_images_parallel()
        
        # Fast position correction
        self.correct_positions_fast()
        
        # Calculate canvas bounds
        min_x = min(tile.corrected_position[0] for tile in self.tiles)
        min_y = min(tile.corrected_position[1] for tile in self.tiles)
        max_x = max(tile.corrected_position[0] + tile.size[0] for tile in self.tiles)
        max_y = max(tile.corrected_position[1] + tile.size[1] for tile in self.tiles)
        
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        offset = (min_x, min_y)
        
        logger.info(f"Canvas size: {canvas_width}x{canvas_height}")
        
        # Initialize canvas
        canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        weights = np.zeros((canvas_height, canvas_width), dtype=np.float32) if self.fusion_method == "linear_blending" else None
        
        # Ultra-fast fusion
        logger.info("Performing ultra-fast fusion...")
        fusion_start = time.time()
        
        for i, tile in enumerate(self.tiles):
            x_offset = int(tile.corrected_position[0] - offset[0])
            y_offset = int(tile.corrected_position[1] - offset[1])
            
            self.ultra_fast_fusion(canvas, weights, tile.image, x_offset, y_offset)
            
            if (i + 1) % 30 == 0:
                logger.info(f"Fused {i + 1}/{len(self.tiles)} tiles")
        
        fusion_time = time.time() - fusion_start
        total_time = time.time() - start_time
        
        logger.info(f"Fusion completed in {fusion_time:.2f}s")
        logger.info(f"Total stitching time: {total_time:.2f}s")
        
        # Convert to uint8
        result = np.clip(canvas, 0, 255).astype(np.uint8)
        return result


def main():
    """Command line interface"""
    parser = argparse.ArgumentParser(description="Ultra-Fast ImageJ-compatible Image Stitching")
    parser.add_argument("config_file", help="Path to TileConfiguration.txt file")
    parser.add_argument("-o", "--output", default="fast_result.jpg", help="Output filename")
    parser.add_argument("-m", "--method", default="linear_blending", 
                       choices=["linear_blending", "max"], help="Fusion method")
    parser.add_argument("--no-correction", action="store_true", 
                       help="Disable position correction")
    parser.add_argument("-j", "--threads", type=int, help="Number of threads")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Create fast stitcher
        stitcher = FastStitcher(
            fusion_method=args.method,
            enable_position_correction=not args.no_correction,
            num_threads=args.threads
        )
        
        # Parse configuration
        stitcher.parse_tile_configuration(args.config_file)
        
        # Perform stitching
        result = stitcher.stitch_images_fast()
        
        # Save result
        cv2.imwrite(args.output, result)
        logger.info(f"Fast stitching result saved: {args.output}")
        logger.info(f"Result size: {result.shape[1]}x{result.shape[0]} pixels")
        
        return 0
        
    except Exception as e:
        logger.error(f"Stitching failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
