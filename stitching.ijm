// ImageJ Macro for grid/collection stitching with scale bar
// 设置批处理模式
setBatchMode(true);
filename = "Image_51";
// 定义输入和输出文件夹路径 - 直接指定路径
input_dir = "D:\\images\\" + filename + "\\";  // 注意结尾要加反斜杠
output_dir = "D:\\images\\";               // 注意结尾要加反斜杠

// 清理内存
run("Collect Garbage");


// 执行网格/集合拼接
run("Grid/Collection stitching", 
    "type=[Positions from file] " +
    "order=[Defined by TileConfiguration] " +
    "directory=[" + input_dir + "] " +
    "layout_file=TileConfiguration.txt " +
    "fusion_method=[Linear Blending] " +
    "regression_threshold=0.30 " +
    "max/avg_displacement_threshold=2.50 " +
    "absolute_displacement_threshold=3.50 " +
    "compute_overlap " +
    "subpixel_accuracy " +
    "use_all_cpus "+
    "computation_parameters=[Save computation time (but use more RAM)] " +
    "image_output=[Fuse and display]");




// 设置比例尺
run("Set Scale...", "distance=1000 known=0.978 unit=mm");

// 添加比例尺
run("Scale Bar...", "width=1 height=100 font=200 color=Red background=None location=[Lower Right] bold overlay");

// 保存结果为质量为75%的JPEG
output_file = output_dir + filename + ".jpg";
run("Jpeg...", "quality=75 save=[" + output_file + "]");

// 关闭所有图像
close("*");

// 清理内存
run("Collect Garbage");

// 显示完成消息
//showMessage("任务完成", "图像拼接、添加比例尺和保存操作已完成！\n结果保存在: " + output_dir);
// 在Console中显示完成消息而不是弹出对话框

print("任务完成");
print("图像拼接、添加比例尺和保存操作已完成！");
print("结果保存在: " + output_file);


// 退出批处理模式
setBatchMode(false);