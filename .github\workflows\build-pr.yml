name: build PR

on:
  pull_request:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          java-version: '8'
          distribution: 'zulu'
          cache: 'maven'
      - name: Set up CI environment
        run: .github/setup.sh
      - name: Execute the build
        run: .github/build.sh
