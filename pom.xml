<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.scijava</groupId>
		<artifactId>pom-scijava</artifactId>
		<version>34.1.0</version>
		<relativePath />
	</parent>

	<groupId>sc.fiji</groupId>
	<artifactId>Stitching_</artifactId>
	<version>3.1.10-SNAPSHOT</version>

	<name>Stitching</name>
	<description>A plugin for reconstructing big images/stacks from an arbitrary number of tiled input images/stacks.</description>
	<url>https://imagej.net/Stitching</url>
	<inceptionYear>2007</inceptionYear>
	<organization>
		<name>Fiji</name>
		<url>http://fiji.sc/</url>
	</organization>
	<licenses>
		<license>
			<name>GNU General Public License v2+</name>
			<url>http://www.gnu.org/licenses/gpl.html</url>
			<distribution>repo</distribution>
		</license>
	</licenses>

	<developers>
		<developer>
			<id>StephanPreibisch</id>
			<name>Stephan Preibisch</name>
			<url>https://imagej.net/User:StephanP</url>
			<roles>
				<role>founder</role>
				<role>lead</role>
				<role>developer</role>
				<role>debugger</role>
				<role>reviewer</role>
				<role>support</role>
				<role>maintainer</role>
			</roles>
		</developer>
		<developer>
			<id>ctrueden</id>
			<name>Curtis Rueden</name>
			<url>https://imagej.net/User:Rueden</url>
			<roles>
				<role>debugger</role>
				<role>support</role>
				<role>maintainer</role>
			</roles>
		</developer>
	</developers>
	<contributors>
		<contributor>
			<name>Johannes Schindelin</name>
			<url>https://imagej.net/User:Schindelin</url>
			<properties><id>dscho</id></properties>
		</contributor>
		<contributor>
			<name>Ignacio Arganda-Carreras</name>
			<url>https://imagej.net/User:Iarganda</url>
			<properties><id>iarganda</id></properties>
		</contributor>
		<contributor>
			<name>Niko Ehrenfeuchter</name>
			<url>https://imagej.net/User:Ehrenfeu</url>
			<properties><id>ehrenfeu</id></properties>
		</contributor>
		<contributor>
			<name>Mark Hiner</name>
			<url>https://imagej.net/User:Hinerm</url>
			<properties><id>hinerm</id></properties>
		</contributor>
		<contributor>
			<name>Kevin Mader</name>
			<url>https://imagej.net/User:Ksmader</url>
			<properties><id>kmader</id></properties>
		</contributor>
		<contributor>
			<name>Stephan Saalfeld</name>
			<url>https://imagej.net/User:Saalfeld</url>
			<properties><id>axtimwalde</id></properties>
		</contributor>
		<contributor><name>John Lapage</name></contributor>
	</contributors>

	<mailingLists>
		<mailingList>
			<name>Image.sc Forum</name>
			<archive>https://forum.image.sc/tag/fiji</archive>
		</mailingList>
	</mailingLists>

	<scm>
		<connection>scm:git:https://github.com/fiji/Stitching</connection>
		<developerConnection>scm:git:**************:fiji/Stitching</developerConnection>
		<tag>HEAD</tag>
		<url>https://github.com/fiji/Stitching</url>
	</scm>
	<issueManagement>
		<system>GitHub Issues</system>
		<url>https://github.com/fiji/Stitching/issues</url>
	</issueManagement>
	<ciManagement>
		<system>GitHub Actions</system>
		<url>https://github.com/fiji/Stitching/actions</url>
	</ciManagement>

	<properties>
		<license.licenseName>gpl_v2</license.licenseName>
		<license.copyrightOwners>Fiji developers.</license.copyrightOwners>
		<license.projectName>Fiji distribution of ImageJ for the life sciences.</license.projectName>

		<!-- NB: Deploy releases to the SciJava Maven repository. -->
		<releaseProfiles>sign,deploy-to-scijava</releaseProfiles>
	</properties>

	<repositories>
		<repository>
			<id>scijava.public</id>
			<url>https://maven.scijava.org/content/groups/public</url>
		</repository>
	</repositories>

	<dependencies>
		<!-- Fiji dependencies -->
		<dependency>
			<groupId>sc.fiji</groupId>
			<artifactId>fiji-lib</artifactId>
		</dependency>
		<dependency>
			<groupId>sc.fiji</groupId>
			<artifactId>Fiji_Plugins</artifactId>
		</dependency>
		<dependency>
			<groupId>sc.fiji</groupId>
			<artifactId>legacy-imglib1</artifactId>
		</dependency>

		<!-- ImageJ dependencies -->
		<dependency>
			<groupId>net.imagej</groupId>
			<artifactId>ij</artifactId>
		</dependency>

		<!-- ImgLib2 dependencies -->
		<dependency>
			<groupId>net.imglib2</groupId>
			<artifactId>imglib2</artifactId>
		</dependency>
		<dependency>
			<groupId>net.imglib2</groupId>
			<artifactId>imglib2-algorithm</artifactId>
		</dependency>
		<dependency>
			<groupId>net.imglib2</groupId>
			<artifactId>imglib2-ij</artifactId>
		</dependency>

		<!-- OME dependencies -->
		<dependency>
			<groupId>ome</groupId>
			<artifactId>bio-formats_plugins</artifactId>
		</dependency>
		<dependency>
			<groupId>ome</groupId>
			<artifactId>formats-api</artifactId>
		</dependency>
		<dependency>
			<groupId>ome</groupId>
			<artifactId>formats-bsd</artifactId>
		</dependency>
		<dependency>
			<groupId>org.openmicroscopy</groupId>
			<artifactId>ome-common</artifactId>
		</dependency>
		<dependency>
			<groupId>org.openmicroscopy</groupId>
			<artifactId>ome-xml</artifactId>
		</dependency>

		<!-- Third party dependencies -->
		<dependency>
			<groupId>edu.mines</groupId>
			<artifactId>mines-jtk</artifactId>
		</dependency>
		<dependency>
			<groupId>mpicbg</groupId>
			<artifactId>mpicbg</artifactId>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
</project>
