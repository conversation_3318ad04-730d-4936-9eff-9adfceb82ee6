#!/usr/bin/env python3
"""
Test script for ImageJ-compatible stitching implementation
"""

import cv2
import numpy as np
import os
import tempfile
import shutil
from imagej_stitching_python import (
    TileConfigurationParser, 
    ImageJStitcher, 
    stitch_from_configuration,
    create_sample_configuration
)
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_tile_configuration_parser():
    """Test TileConfiguration.txt parsing"""
    print("Testing TileConfiguration.txt parser...")
    
    # Create temporary config file
    config_content = """# Define the number of dimensions we are working on
dim = 2

# Define the image coordinates
s_0001.jpg ; ; (-0.000,0.000)
s_0002.jpg ; ; (2203.200,0.000)
s_0003.jpg ; ; (4406.400,0.000)
s_0004.jpg ; ; (6609.600,0.000)
s_0005.jpg ; ; (8812.800,0.000)
s_0006.jpg ; ; (11016.000,0.000)
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(config_content)
        config_path = f.name
    
    try:
        parser = TileConfigurationParser()
        tiles = parser.parse_file(config_path, "/dummy/path")
        
        assert len(tiles) == 6, f"Expected 6 tiles, got {len(tiles)}"
        assert parser.dimension == 2, f"Expected dimension 2, got {parser.dimension}"
        
        # Check first tile
        first_tile = tiles[0]
        assert first_tile.filename.endswith("s_0001.jpg")
        assert first_tile.position == (0.0, 0.0)
        
        # Check last tile
        last_tile = tiles[-1]
        assert last_tile.filename.endswith("s_0006.jpg")
        assert last_tile.position == (11016.0, 0.0)
        
        print("✓ TileConfiguration.txt parser test passed")
        
    finally:
        os.unlink(config_path)

def test_sample_creation_and_stitching():
    """Test sample creation and stitching"""
    print("Testing sample creation and stitching...")
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Create sample data
        config_path = create_sample_configuration(temp_dir)
        
        # Verify files were created
        assert os.path.exists(config_path), "Configuration file not created"
        
        expected_files = ["s_0001.jpg", "s_0002.jpg", "s_0003.jpg", "s_0004.jpg"]
        for filename in expected_files:
            filepath = os.path.join(temp_dir, filename)
            assert os.path.exists(filepath), f"Sample image {filename} not created"
        
        # Test stitching with different methods
        fusion_methods = ["linear_blending", "average", "max", "min"]
        
        for method in fusion_methods:
            print(f"  Testing fusion method: {method}")
            
            output_path = os.path.join(temp_dir, f"result_{method}.jpg")
            
            result = stitch_from_configuration(
                config_path=config_path,
                fusion_method=method,
                output_path=output_path
            )
            
            # Verify result
            assert result is not None, f"Stitching failed for method {method}"
            assert result.shape[0] > 0 and result.shape[1] > 0, f"Invalid result shape for {method}"
            assert os.path.exists(output_path), f"Output file not created for {method}"
            
            # Check that result is larger than individual tiles
            sample_tile = cv2.imread(os.path.join(temp_dir, "s_0001.jpg"), cv2.IMREAD_GRAYSCALE)
            assert result.shape[0] > sample_tile.shape[0], f"Result height should be larger than tile for {method}"
            assert result.shape[1] > sample_tile.shape[1], f"Result width should be larger than tile for {method}"
        
        print("✓ Sample creation and stitching test passed")
        
    finally:
        shutil.rmtree(temp_dir)

def test_real_world_scenario():
    """Test with a more realistic scenario"""
    print("Testing real-world scenario...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Create a more complex scene with overlapping tiles
        scene_size = (1500, 1000)
        tile_size = (600, 400)
        overlap = 150
        
        # Create synthetic scene with features
        scene = np.zeros(scene_size, dtype=np.uint8)
        
        # Add gradient background
        for y in range(scene_size[0]):
            for x in range(scene_size[1]):
                scene[y, x] = int(255 * np.sin(x * 0.01) * np.cos(y * 0.01) * 0.5 + 128)
        
        # Add some distinctive features
        features = [
            ((200, 150), 80, 255),   # Circle
            ((800, 300), 60, 200),   # Circle
            ((400, 600), 70, 180),   # Circle
            ((1000, 700), 50, 220),  # Circle
        ]
        
        for (center, radius, intensity) in features:
            cv2.circle(scene, center, radius, intensity, -1)
        
        # Add some rectangles
        cv2.rectangle(scene, (300, 200), (500, 400), 255, -1)
        cv2.rectangle(scene, (700, 500), (900, 700), 150, -1)
        
        # Create overlapping tiles in a 3x2 grid
        tiles_info = []
        tile_id = 1
        
        for row in range(2):
            for col in range(3):
                start_x = col * (tile_size[1] - overlap)
                start_y = row * (tile_size[0] - overlap)
                
                end_x = min(start_x + tile_size[1], scene_size[1])
                end_y = min(start_y + tile_size[0], scene_size[0])
                
                # Extract tile
                tile = scene[start_y:end_y, start_x:end_x]
                
                # Save tile
                filename = f"tile_{tile_id:03d}.jpg"
                filepath = os.path.join(temp_dir, filename)
                cv2.imwrite(filepath, tile)
                
                tiles_info.append((filename, start_x, start_y))
                tile_id += 1
        
        # Create configuration file
        config_path = os.path.join(temp_dir, "TileConfiguration.txt")
        with open(config_path, 'w') as f:
            f.write("# Define the number of dimensions we are working on\n")
            f.write("dim = 2\n")
            f.write("\n")
            f.write("# Define the image coordinates\n")
            
            for filename, x, y in tiles_info:
                f.write(f"{filename} ; ; ({x:.1f},{y:.1f})\n")
        
        # Save original scene for comparison
        original_path = os.path.join(temp_dir, "original_scene.jpg")
        cv2.imwrite(original_path, scene)
        
        # Perform stitching
        result_path = os.path.join(temp_dir, "stitched_result.jpg")
        result = stitch_from_configuration(
            config_path=config_path,
            fusion_method="linear_blending",
            blend_alpha=2.0,
            output_path=result_path
        )
        
        # Verify result
        assert result is not None, "Stitching failed"
        assert os.path.exists(result_path), "Result file not created"
        
        # Check that result dimensions are reasonable
        expected_width = 3 * (tile_size[1] - overlap) + overlap
        expected_height = 2 * (tile_size[0] - overlap) + overlap
        
        # Allow some tolerance
        width_diff = abs(result.shape[1] - expected_width)
        height_diff = abs(result.shape[0] - expected_height)
        
        assert width_diff < 50, f"Width difference too large: {width_diff}"
        assert height_diff < 50, f"Height difference too large: {height_diff}"
        
        print(f"  Original scene: {scene_size}")
        print(f"  Stitched result: {result.shape}")
        print(f"  Expected size: {expected_height}x{expected_width}")
        
        print("✓ Real-world scenario test passed")
        
    finally:
        shutil.rmtree(temp_dir)

def test_edge_cases():
    """Test edge cases and error handling"""
    print("Testing edge cases...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Test with non-existent config file
        try:
            stitch_from_configuration("non_existent_file.txt")
            assert False, "Should have raised an exception"
        except FileNotFoundError:
            pass  # Expected
        
        # Test with empty config file
        empty_config = os.path.join(temp_dir, "empty.txt")
        with open(empty_config, 'w') as f:
            f.write("# Empty config\ndim = 2\n")
        
        try:
            stitch_from_configuration(empty_config)
            assert False, "Should have raised an exception for empty config"
        except ValueError:
            pass  # Expected
        
        # Test with malformed config file
        malformed_config = os.path.join(temp_dir, "malformed.txt")
        with open(malformed_config, 'w') as f:
            f.write("dim = 2\ninvalid line without coordinates\n")
        
        parser = TileConfigurationParser()
        tiles = parser.parse_file(malformed_config)
        assert len(tiles) == 0, "Should parse 0 tiles from malformed config"
        
        print("✓ Edge cases test passed")
        
    finally:
        shutil.rmtree(temp_dir)

def run_all_tests():
    """Run all tests"""
    print("Running ImageJ Stitching Python Implementation Tests")
    print("=" * 60)
    
    tests = [
        test_tile_configuration_parser,
        test_sample_creation_and_stitching,
        test_real_world_scenario,
        test_edge_cases
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False

def create_demo():
    """Create a demonstration with visual output"""
    print("Creating demonstration...")
    
    demo_dir = "stitching_demo"
    os.makedirs(demo_dir, exist_ok=True)
    
    # Create sample data
    config_path = create_sample_configuration(demo_dir)
    
    # Test all fusion methods
    fusion_methods = ["linear_blending", "average", "max", "min"]
    
    for method in fusion_methods:
        print(f"Creating demo with {method} fusion...")
        
        output_path = os.path.join(demo_dir, f"demo_{method}.jpg")
        
        result = stitch_from_configuration(
            config_path=config_path,
            fusion_method=method,
            output_path=output_path
        )
        
        print(f"  Result saved: {output_path}")
    
    print(f"\nDemo files created in: {demo_dir}/")
    print("Files created:")
    for file in os.listdir(demo_dir):
        print(f"  - {file}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        create_demo()
    else:
        success = run_all_tests()
        sys.exit(0 if success else 1)
