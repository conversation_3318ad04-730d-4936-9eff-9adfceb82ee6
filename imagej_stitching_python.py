#!/usr/bin/env python3
"""
ImageJ Stitching Algorithm Implementation in Python
Compatible with ImageJ TileConfiguration.txt format

This implementation provides the same functionality as ImageJ Stitching plugin
with support for position-from-file mode using TileConfiguration.txt format.

Author: Python implementation based on ImageJ Stitching Plugin
License: GPL v2+
"""

import cv2
import numpy as np
import os
import re
import argparse
from typing import List, Tuple, Dict, Optional
import logging
from dataclasses import dataclass
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TileInfo:
    """Information about each image tile"""
    filename: str
    position: Tuple[float, float]
    image: Optional[np.ndarray] = None
    size: Optional[Tuple[int, int]] = None

class TileConfigurationParser:
    """Parser for ImageJ TileConfiguration.txt format"""
    
    def __init__(self):
        self.dimension = 2
        self.tiles: List[TileInfo] = []
    
    def parse_file(self, config_path: str, image_directory: str = None) -> List[TileInfo]:
        """
        Parse TileConfiguration.txt file
        
        Args:
            config_path: Path to TileConfiguration.txt
            image_directory: Directory containing images (if None, use config file directory)
            
        Returns:
            List of TileInfo objects
        """
        if image_directory is None:
            image_directory = os.path.dirname(config_path)
        
        self.tiles = []
        
        with open(config_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue
                
                # Parse dimension
                if line.startswith('dim'):
                    match = re.search(r'dim\s*=\s*(\d+)', line)
                    if match:
                        self.dimension = int(match.group(1))
                        logger.info(f"Detected {self.dimension}D configuration")
                    continue
                
                # Parse tile information
                # Format: filename ; ; (x, y) or filename ; ; (x, y, z)
                if ';' in line:
                    try:
                        parts = line.split(';')
                        if len(parts) >= 3:
                            filename = parts[0].strip()
                            position_str = parts[2].strip()
                            
                            # Extract coordinates from parentheses
                            coord_match = re.search(r'\(([-+]?\d*\.?\d+),\s*([-+]?\d*\.?\d+)(?:,\s*([-+]?\d*\.?\d+))?\)', position_str)
                            if coord_match:
                                x = float(coord_match.group(1))
                                y = float(coord_match.group(2))
                                
                                # For 2D, ignore z coordinate
                                position = (x, y)
                                
                                # Create full path
                                full_path = os.path.join(image_directory, filename)
                                
                                tile = TileInfo(filename=full_path, position=position)
                                self.tiles.append(tile)
                                
                                logger.debug(f"Parsed tile: {filename} at {position}")
                            else:
                                logger.warning(f"Could not parse coordinates in line {line_num}: {line}")
                    except Exception as e:
                        logger.error(f"Error parsing line {line_num}: {line} - {e}")
        
        logger.info(f"Parsed {len(self.tiles)} tiles from configuration file")
        return self.tiles

class ImageJStitcher:
    """
    ImageJ-compatible stitching implementation
    """
    
    def __init__(self, fusion_method: str = "linear_blending", 
                 blend_alpha: float = 1.5,
                 ignore_zero_values: bool = True):
        """
        Initialize stitcher with ImageJ-compatible parameters
        
        Args:
            fusion_method: Fusion method ("linear_blending", "average", "max", "min")
            blend_alpha: Blending parameter for linear blending
            ignore_zero_values: Whether to ignore zero pixel values
        """
        self.fusion_method = fusion_method
        self.blend_alpha = blend_alpha
        self.ignore_zero_values = ignore_zero_values
        self.tiles: List[TileInfo] = []
    
    def load_tiles(self, tiles: List[TileInfo]) -> None:
        """Load images for all tiles"""
        self.tiles = []
        
        for tile in tiles:
            if not os.path.exists(tile.filename):
                logger.warning(f"Image not found: {tile.filename}")
                continue
            
            # Load image
            img = cv2.imread(tile.filename, cv2.IMREAD_GRAYSCALE)
            if img is None:
                logger.warning(f"Failed to load image: {tile.filename}")
                continue
            
            # Create new tile info with loaded image
            tile_info = TileInfo(
                filename=tile.filename,
                position=tile.position,
                image=img,
                size=(img.shape[1], img.shape[0])  # (width, height)
            )
            
            self.tiles.append(tile_info)
            logger.info(f"Loaded tile: {os.path.basename(tile.filename)} "
                       f"({tile_info.size}) at position {tile.position}")
    
    def calculate_canvas_bounds(self) -> Tuple[Tuple[int, int], Tuple[float, float]]:
        """Calculate the bounds of the final canvas"""
        if not self.tiles:
            return (0, 0), (0.0, 0.0)
        
        # Find min/max coordinates
        min_x = min(tile.position[0] for tile in self.tiles)
        min_y = min(tile.position[1] for tile in self.tiles)
        max_x = max(tile.position[0] + tile.size[0] for tile in self.tiles)
        max_y = max(tile.position[1] + tile.size[1] for tile in self.tiles)
        
        # Calculate canvas size
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        
        # Offset to translate all positions to positive coordinates
        offset = (min_x, min_y)
        
        logger.info(f"Canvas size: {canvas_width}x{canvas_height}, offset: {offset}")
        
        return (canvas_width, canvas_height), offset
    
    def create_distance_weight(self, img_shape: Tuple[int, int]) -> np.ndarray:
        """
        Create distance-based weight map for linear blending
        Compatible with ImageJ's blending algorithm
        """
        height, width = img_shape
        
        # Create coordinate grids
        y_coords, x_coords = np.ogrid[:height, :width]
        
        # Calculate distance from edges
        dist_from_left = x_coords
        dist_from_right = width - 1 - x_coords
        dist_from_top = y_coords
        dist_from_bottom = height - 1 - y_coords
        
        # Minimum distance to any edge
        dist_to_edge = np.minimum(
            np.minimum(dist_from_left, dist_from_right),
            np.minimum(dist_from_top, dist_from_bottom)
        )
        
        # Apply alpha parameter (similar to ImageJ)
        if self.blend_alpha > 0:
            weights = np.power(dist_to_edge + 1, 1.0 / self.blend_alpha)
        else:
            weights = dist_to_edge + 1
        
        # Normalize weights
        max_weight = np.max(weights)
        if max_weight > 0:
            weights = weights / max_weight
        
        return weights.astype(np.float32)
    
    def linear_blending_fusion(self, canvas: np.ndarray, weight_canvas: np.ndarray,
                              img: np.ndarray, x_offset: int, y_offset: int) -> None:
        """
        Apply linear blending fusion (ImageJ-compatible)
        """
        h, w = img.shape
        
        # Create blending weights
        blend_weights = self.create_distance_weight((h, w))
        
        # Apply blending
        for y in range(h):
            canvas_y = y + y_offset
            if canvas_y < 0 or canvas_y >= canvas.shape[0]:
                continue
                
            for x in range(w):
                canvas_x = x + x_offset
                if canvas_x < 0 or canvas_x >= canvas.shape[1]:
                    continue
                
                pixel_value = img[y, x]
                
                # Skip zero values if requested
                if self.ignore_zero_values and pixel_value == 0:
                    continue
                
                weight = blend_weights[y, x]
                current_weight = weight_canvas[canvas_y, canvas_x]
                
                if current_weight == 0:
                    # First pixel at this location
                    canvas[canvas_y, canvas_x] = pixel_value
                    weight_canvas[canvas_y, canvas_x] = weight
                else:
                    # Blend with existing pixel
                    total_weight = current_weight + weight
                    if total_weight > 0:
                        canvas[canvas_y, canvas_x] = (
                            (canvas[canvas_y, canvas_x] * current_weight + 
                             pixel_value * weight) / total_weight
                        )
                        weight_canvas[canvas_y, canvas_x] = total_weight
    
    def average_fusion(self, canvas: np.ndarray, count_canvas: np.ndarray,
                      img: np.ndarray, x_offset: int, y_offset: int) -> None:
        """Apply average fusion"""
        h, w = img.shape
        
        for y in range(h):
            canvas_y = y + y_offset
            if canvas_y < 0 or canvas_y >= canvas.shape[0]:
                continue
                
            for x in range(w):
                canvas_x = x + x_offset
                if canvas_x < 0 or canvas_x >= canvas.shape[1]:
                    continue
                
                pixel_value = img[y, x]
                
                # Skip zero values if requested
                if self.ignore_zero_values and pixel_value == 0:
                    continue
                
                count = count_canvas[canvas_y, canvas_x]
                
                if count == 0:
                    canvas[canvas_y, canvas_x] = pixel_value
                    count_canvas[canvas_y, canvas_x] = 1
                else:
                    # Running average
                    canvas[canvas_y, canvas_x] = (
                        (canvas[canvas_y, canvas_x] * count + pixel_value) / (count + 1)
                    )
                    count_canvas[canvas_y, canvas_x] = count + 1
    
    def max_fusion(self, canvas: np.ndarray, img: np.ndarray, 
                   x_offset: int, y_offset: int) -> None:
        """Apply maximum intensity fusion"""
        h, w = img.shape
        
        for y in range(h):
            canvas_y = y + y_offset
            if canvas_y < 0 or canvas_y >= canvas.shape[0]:
                continue
                
            for x in range(w):
                canvas_x = x + x_offset
                if canvas_x < 0 or canvas_x >= canvas.shape[1]:
                    continue
                
                pixel_value = img[y, x]
                
                # Skip zero values if requested
                if self.ignore_zero_values and pixel_value == 0:
                    continue
                
                canvas[canvas_y, canvas_x] = max(canvas[canvas_y, canvas_x], pixel_value)
    
    def min_fusion(self, canvas: np.ndarray, mask_canvas: np.ndarray,
                   img: np.ndarray, x_offset: int, y_offset: int) -> None:
        """Apply minimum intensity fusion"""
        h, w = img.shape
        
        for y in range(h):
            canvas_y = y + y_offset
            if canvas_y < 0 or canvas_y >= canvas.shape[0]:
                continue
                
            for x in range(w):
                canvas_x = x + x_offset
                if canvas_x < 0 or canvas_x >= canvas.shape[1]:
                    continue
                
                pixel_value = img[y, x]
                
                # Skip zero values if requested
                if self.ignore_zero_values and pixel_value == 0:
                    continue
                
                if mask_canvas[canvas_y, canvas_x] == 0:
                    # First pixel at this location
                    canvas[canvas_y, canvas_x] = pixel_value
                    mask_canvas[canvas_y, canvas_x] = 1
                else:
                    canvas[canvas_y, canvas_x] = min(canvas[canvas_y, canvas_x], pixel_value)

    def stitch_images(self) -> np.ndarray:
        """
        Perform image stitching using predefined positions

        Returns:
            Stitched panorama image
        """
        if not self.tiles:
            raise ValueError("No tiles loaded")

        logger.info(f"Starting stitching of {len(self.tiles)} tiles using {self.fusion_method}")

        # Calculate canvas size and offset
        (canvas_width, canvas_height), canvas_offset = self.calculate_canvas_bounds()

        # Initialize canvas
        canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)

        # Initialize auxiliary arrays based on fusion method
        if self.fusion_method == "linear_blending":
            weight_canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        elif self.fusion_method == "average":
            count_canvas = np.zeros((canvas_height, canvas_width), dtype=np.int32)
        elif self.fusion_method == "min":
            mask_canvas = np.zeros((canvas_height, canvas_width), dtype=np.uint8)

        # Place each tile on the canvas
        for i, tile in enumerate(self.tiles):
            # Calculate position on canvas
            x_offset = int(tile.position[0] - canvas_offset[0])
            y_offset = int(tile.position[1] - canvas_offset[1])

            logger.info(f"Placing tile {i+1}/{len(self.tiles)}: "
                       f"{os.path.basename(tile.filename)} at canvas position ({x_offset}, {y_offset})")

            # Apply fusion method
            if self.fusion_method == "linear_blending":
                self.linear_blending_fusion(canvas, weight_canvas,
                                          tile.image.astype(np.float32),
                                          x_offset, y_offset)
            elif self.fusion_method == "average":
                self.average_fusion(canvas, count_canvas,
                                  tile.image.astype(np.float32),
                                  x_offset, y_offset)
            elif self.fusion_method == "max":
                self.max_fusion(canvas, tile.image.astype(np.float32),
                              x_offset, y_offset)
            elif self.fusion_method == "min":
                self.min_fusion(canvas, mask_canvas,
                              tile.image.astype(np.float32),
                              x_offset, y_offset)
            else:
                raise ValueError(f"Unknown fusion method: {self.fusion_method}")

        # Convert back to uint8
        canvas = np.clip(canvas, 0, 255).astype(np.uint8)

        logger.info("Stitching completed successfully!")
        return canvas


def stitch_from_configuration(config_path: str,
                            image_directory: str = None,
                            fusion_method: str = "linear_blending",
                            blend_alpha: float = 1.5,
                            ignore_zero_values: bool = True,
                            output_path: str = "stitched_result.jpg") -> np.ndarray:
    """
    Stitch images using TileConfiguration.txt file (ImageJ compatible)

    Args:
        config_path: Path to TileConfiguration.txt file
        image_directory: Directory containing images (if None, use config file directory)
        fusion_method: Fusion method ("linear_blending", "average", "max", "min")
        blend_alpha: Blending parameter for linear blending
        ignore_zero_values: Whether to ignore zero pixel values
        output_path: Output file path

    Returns:
        Stitched image as numpy array
    """
    # Parse configuration file
    parser = TileConfigurationParser()
    tiles = parser.parse_file(config_path, image_directory)

    if not tiles:
        raise ValueError("No valid tiles found in configuration file")

    # Create stitcher
    stitcher = ImageJStitcher(
        fusion_method=fusion_method,
        blend_alpha=blend_alpha,
        ignore_zero_values=ignore_zero_values
    )

    # Load tiles
    stitcher.load_tiles(tiles)

    # Perform stitching
    result = stitcher.stitch_images()

    # Save result
    if output_path:
        cv2.imwrite(output_path, result)
        logger.info(f"Stitched image saved as: {output_path}")

    return result


def create_sample_configuration(output_dir: str = "sample_tiles"):
    """
    Create a sample TileConfiguration.txt and test images for demonstration
    """
    os.makedirs(output_dir, exist_ok=True)

    # Create sample images
    tile_size = (512, 512)
    overlap = 100  # pixels

    # Generate 2x2 grid of tiles
    positions = [
        (0, 0),                                    # Top-left
        (tile_size[0] - overlap, 0),               # Top-right
        (0, tile_size[1] - overlap),               # Bottom-left
        (tile_size[0] - overlap, tile_size[1] - overlap)  # Bottom-right
    ]

    # Create synthetic scene
    scene_width, scene_height = 1024, 1024
    scene = np.zeros((scene_height, scene_width), dtype=np.uint8)

    # Add gradient background
    for y in range(scene_height):
        for x in range(scene_width):
            scene[y, x] = int(255 * (x + y) / (scene_width + scene_height))

    # Add some features
    cv2.circle(scene, (256, 256), 80, 255, -1)
    cv2.circle(scene, (768, 256), 60, 128, -1)
    cv2.circle(scene, (256, 768), 70, 200, -1)
    cv2.circle(scene, (768, 768), 50, 180, -1)
    cv2.rectangle(scene, (400, 400), (624, 624), 255, -1)

    # Extract tiles
    tile_filenames = []
    for i, (start_x, start_y) in enumerate(positions):
        end_x = min(start_x + tile_size[0], scene_width)
        end_y = min(start_y + tile_size[1], scene_height)

        tile = scene[start_y:end_y, start_x:end_x]

        filename = f"s_{i+1:04d}.jpg"
        filepath = os.path.join(output_dir, filename)
        cv2.imwrite(filepath, tile)

        tile_filenames.append((filename, start_x, start_y))
        logger.info(f"Created sample tile: {filename}")

    # Create TileConfiguration.txt
    config_path = os.path.join(output_dir, "TileConfiguration.txt")
    with open(config_path, 'w') as f:
        f.write("# Define the number of dimensions we are working on\n")
        f.write("dim = 2\n")
        f.write("\n")
        f.write("# Define the image coordinates\n")

        for filename, x, y in tile_filenames:
            f.write(f"{filename} ; ; ({x:.3f},{y:.3f})\n")

    logger.info(f"Created sample configuration: {config_path}")

    # Save original scene for comparison
    scene_path = os.path.join(output_dir, "original_scene.jpg")
    cv2.imwrite(scene_path, scene)
    logger.info(f"Created original scene: {scene_path}")

    return config_path


def main():
    """Command line interface"""
    parser = argparse.ArgumentParser(
        description="ImageJ-compatible Image Stitching using TileConfiguration.txt",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic stitching
  python imagej_stitching_python.py TileConfiguration.txt -o result.jpg

  # Specify image directory
  python imagej_stitching_python.py config.txt -d /path/to/images/ -o result.jpg

  # Use different fusion method
  python imagej_stitching_python.py config.txt -m average -o result.jpg

  # Create sample data for testing
  python imagej_stitching_python.py --create-sample
        """
    )

    parser.add_argument("config_file", nargs='?',
                       help="Path to TileConfiguration.txt file")
    parser.add_argument("-d", "--image-directory",
                       help="Directory containing images (default: same as config file)")
    parser.add_argument("-o", "--output", default="stitched_result.jpg",
                       help="Output filename (default: stitched_result.jpg)")
    parser.add_argument("-m", "--method", default="linear_blending",
                       choices=["linear_blending", "average", "max", "min"],
                       help="Fusion method (default: linear_blending)")
    parser.add_argument("-a", "--alpha", type=float, default=1.5,
                       help="Blending alpha parameter (default: 1.5)")
    parser.add_argument("--ignore-zero", action="store_true", default=True,
                       help="Ignore zero pixel values (default: True)")
    parser.add_argument("--create-sample", action="store_true",
                       help="Create sample tiles and configuration for testing")
    parser.add_argument("-v", "--verbose", action="store_true",
                       help="Enable verbose logging")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        if args.create_sample:
            # Create sample data
            config_path = create_sample_configuration()
            print(f"Sample data created. To test stitching, run:")
            print(f"python {__file__} {config_path} -o sample_result.jpg")
            return 0

        if not args.config_file:
            parser.error("config_file is required unless --create-sample is used")

        if not os.path.exists(args.config_file):
            parser.error(f"Configuration file not found: {args.config_file}")

        # Perform stitching
        result = stitch_from_configuration(
            config_path=args.config_file,
            image_directory=args.image_directory,
            fusion_method=args.method,
            blend_alpha=args.alpha,
            ignore_zero_values=args.ignore_zero,
            output_path=args.output
        )

        print(f"Stitching completed successfully!")
        print(f"Result saved as: {args.output}")
        print(f"Result size: {result.shape[1]}x{result.shape[0]} pixels")

        # Display result if possible
        try:
            cv2.imshow("Stitched Result", result)
            print("Press any key to close the display window...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        except:
            print("Display not available, result saved to file only")

        return 0

    except Exception as e:
        logger.error(f"Stitching failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
