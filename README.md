# ImageJ Stitching Plugin

[![Build Status](https://github.com/fiji/Stitching/actions/workflows/build-main.yml/badge.svg)](https://github.com/fiji/Stitching/actions/workflows/build-main.yml)
[![License: GPL v2+](https://img.shields.io/badge/License-GPL%20v2+-blue.svg)](https://www.gnu.org/licenses/gpl-2.0)
[![Version](https://img.shields.io/badge/version-3.1.10--SNAPSHOT-orange.svg)](https://github.com/fiji/Stitching)

A comprehensive ImageJ/Fiji plugin for reconstructing large images from tiled microscopy acquisitions using advanced image registration and fusion algorithms.

## Overview

The Stitching Plugin addresses the challenge of imaging large biological specimens at high resolution when they exceed the microscope's field of view. Modern microscopy often uses motorized stages to create tiled scans, but the physical coordinates from microscope stages are typically not precise enough for accurate image reconstruction.

This plugin provides sophisticated algorithms to:
- **Register** overlapping image tiles using phase correlation and Fourier shift theorem
- **Optimize** tile placement globally across entire datasets
- **Fuse** registered images with various blending methods
- **Handle** 2D-5D datasets including multi-channel and time-lapse data

## Key Features

### 🔬 **Advanced Registration**
- **Phase Correlation**: Uses Fourier Shift Theorem for sub-pixel accurate registration
- **Multi-dimensional**: Supports 2D and 3D image stacks
- **Cross-correlation**: Computes optimal translations (x, y, z) between image pairs
- **Peak Detection**: Analyzes multiple correlation peaks for robust registration

### 🌐 **Global Optimization**
- **Graph-based**: Models tiles as nodes with overlap relationships as edges
- **Iterative Refinement**: Automatically removes poor matches and re-optimizes
- **Error Minimization**: Uses least-squares optimization for consistent tile placement
- **Threshold Control**: Configurable correlation and displacement thresholds

### 🎨 **Flexible Fusion Methods**
- **Linear Blending**: Smooth transitions between overlapping regions
- **Intensity-based**: Average, median, min/max intensity fusion
- **Composite Overlay**: Multi-channel visualization
- **Custom Alpha**: Adjustable blending parameters

### 📊 **Multi-dimensional Support**
- **Channels**: Arbitrary number of fluorescence channels
- **Time-lapse**: Temporal registration and fusion
- **Z-stacks**: 3D volume reconstruction
- **Large Datasets**: Memory-efficient processing for big images

## Installation

### Prerequisites
- **ImageJ/Fiji**: Version 1.53c or later
- **Java**: JDK 8 or higher
- **Memory**: Recommended 4GB+ RAM for large datasets

### Via Fiji Update Sites
1. Open Fiji
2. Go to `Help > Update...`
3. Click `Manage update sites`
4. Enable the "Fiji" update site
5. Restart Fiji

### Manual Installation
1. Download the latest release from [GitHub Releases](https://github.com/fiji/Stitching/releases)
2. Copy `Stitching_.jar` to your ImageJ/Fiji `plugins` folder
3. Restart ImageJ/Fiji

## Usage

The plugin provides several stitching modes accessible via `Plugins > Stitching`:

### 1. **Pairwise Stitching**
For registering two images:
```
Plugins > Stitching > Pairwise stitching
```
- Select two open images
- Configure registration parameters
- Choose fusion method
- Execute stitching

### 2. **Grid/Collection Stitching** (Recommended)
For systematic tile arrangements:
```
Plugins > Stitching > Grid/Collection stitching
```

#### Grid Configuration Options:
- **Grid Size**: Specify rows × columns (e.g., 3×3, 4×5)
- **Overlap**: Percentage overlap between adjacent tiles
- **File Pattern**: Naming convention (e.g., `tile_{ii}.tif`)
- **Directory**: Folder containing tile images

#### Collection Configuration:
- **Tile Configuration File**: Text file defining tile positions
- **Compute Overlap**: Automatic overlap detection
- **Manual Coordinates**: Pre-defined tile positions

### 3. **Advanced Options**

#### Registration Parameters:
- **Correlation Threshold**: Minimum cross-correlation (default: 0.3)
- **Peak Checking**: Number of correlation peaks to analyze (default: 5)
- **Subpixel Accuracy**: Enable for precise sub-pixel registration
- **Displacement Thresholds**: Maximum allowed tile movement

#### Fusion Parameters:
- **Method**: Linear blending, average, median, max/min intensity
- **Alpha**: Blending strength (0.0-1.0)
- **RGB Handling**: Channel processing options
- **Output Format**: 8-bit, 16-bit, or 32-bit

## File Formats

### Supported Input Formats
- **Standard**: TIFF, JPEG, PNG, BMP
- **Microscopy**: LSM, CZI, ND2, OIB, VSI
- **Bio-Formats**: 150+ formats via Bio-Formats library
- **Multi-series**: Files containing multiple image series

### Tile Configuration File Format
```
# Define the number of dimensions we are working on
dim = 2

# Define the image coordinates
tile_001.tif; ; (0.0, 0.0)
tile_002.tif; ; (512.0, 0.0)
tile_003.tif; ; (0.0, 512.0)
tile_004.tif; ; (512.0, 512.0)
```

## Algorithm Details

### Phase Correlation Registration
1. **FFT Computation**: Fast Fourier Transform of overlapping regions
2. **Phase Correlation Matrix**: Normalized cross-power spectrum
3. **Peak Detection**: Identify correlation maxima
4. **Sub-pixel Refinement**: Gaussian fitting for precise localization

### Global Optimization Process
1. **Graph Construction**: Build tile connectivity graph
2. **Initial Registration**: Pairwise registration of overlapping tiles
3. **Global Optimization**: Minimize registration errors across all tiles
4. **Quality Control**: Remove poor matches and re-optimize
5. **Final Placement**: Compute optimal tile positions

### Fusion Pipeline
1. **Boundary Estimation**: Calculate output image dimensions
2. **Memory Management**: Efficient processing of large datasets
3. **Pixel Fusion**: Apply selected fusion method
4. **Blending**: Smooth intensity transitions
5. **Output Generation**: Create final stitched image

## Performance Optimization

### Memory Management
- **Virtual Images**: Process large datasets without loading all tiles
- **Chunked Processing**: Break large operations into manageable pieces
- **Garbage Collection**: Efficient memory cleanup

### Computational Efficiency
- **Multi-threading**: Parallel processing where possible
- **FFT Optimization**: Efficient frequency domain calculations
- **Caching**: Reuse computed results when possible

## Troubleshooting

### Common Issues

#### Poor Registration Quality
- **Solution**: Increase overlap percentage (>20%)
- **Check**: Image quality and contrast
- **Adjust**: Correlation thresholds

#### Memory Errors
- **Solution**: Increase ImageJ memory limit
- **Enable**: Virtual image processing
- **Reduce**: Image bit depth if possible

#### Slow Performance
- **Solution**: Enable multi-threading
- **Reduce**: Number of peaks to check
- **Use**: Downsampling for preview

### Error Messages
- **"Cannot compute phase correlation"**: Check image overlap and quality
- **"Global optimization failed"**: Reduce correlation thresholds
- **"Out of memory"**: Increase heap size or use virtual processing

## Development

### Building from Source
```bash
git clone https://github.com/fiji/Stitching.git
cd Stitching
mvn clean package
```

### Dependencies
- **ImageJ**: Core ImageJ functionality
- **ImgLib2**: N-dimensional image processing
- **Bio-Formats**: Microscopy file format support
- **Fiji**: Additional ImageJ plugins and libraries
- **MPICBG**: Computer vision algorithms

### Project Structure
```
src/main/java/
├── plugin/                 # Main plugin interfaces
│   ├── Stitching_Pairwise.java
│   └── Stitching_Grid.java
├── mpicbg/stitching/       # Core algorithms
│   ├── PairWiseStitchingImgLib.java
│   ├── GlobalOptimization.java
│   └── fusion/
└── stitching/              # Utility classes
    ├── CommonFunctions.java
    └── utils/
```

## Citation

If you use this plugin in your research, please cite:

> Preibisch, S., Saalfeld, S., & Tomancak, P. (2009). Globally optimal stitching of tiled 3D microscopic image acquisitions. *Bioinformatics*, 25(11), 1463-1465.

## Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details on:
- Code style and standards
- Testing requirements
- Pull request process
- Issue reporting

## Support

- **Documentation**: [ImageJ Wiki](https://imagej.net/Stitching)
- **Forum**: [Image.sc Forum](https://forum.image.sc/tag/fiji)
- **Issues**: [GitHub Issues](https://github.com/fiji/Stitching/issues)
- **Email**: Contact the developers via the forum

## License

This project is licensed under the GNU General Public License v2+ - see the [LICENSE.txt](LICENSE.txt) file for details.

## Acknowledgments

- **Stephan Preibisch**: Original developer and algorithm design
- **Fiji Community**: Ongoing development and maintenance
- **ImageJ Team**: Core platform and infrastructure
- **Bio-Formats Team**: File format support

---

**Developed by the Fiji community** | **Part of the ImageJ ecosystem**
