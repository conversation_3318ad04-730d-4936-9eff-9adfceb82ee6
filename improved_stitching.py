#!/usr/bin/env python3
"""
Improved ImageJ Stitching Implementation with Position Correction and Better Fusion
"""

import cv2
import numpy as np
import os
import argparse
from typing import List, Tuple, Dict, Optional
import logging
from dataclasses import dataclass
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TileInfo:
    """Information about each image tile"""
    filename: str
    position: Tuple[float, float]
    corrected_position: Optional[Tuple[float, float]] = None
    image: Optional[np.ndarray] = None
    size: Optional[Tuple[int, int]] = None

class ImprovedStitcher:
    """
    Improved stitching with position correction and optimized fusion
    """
    
    def __init__(self, 
                 fusion_method: str = "linear_blending",
                 enable_position_correction: bool = True,
                 correlation_threshold: float = 0.3,
                 max_shift: int = 200):
        """
        Initialize improved stitcher
        
        Args:
            fusion_method: Fusion method
            enable_position_correction: Enable position correction using feature matching
            correlation_threshold: Minimum correlation for position correction
            max_shift: Maximum allowed position shift in pixels
        """
        self.fusion_method = fusion_method
        self.enable_position_correction = enable_position_correction
        self.correlation_threshold = correlation_threshold
        self.max_shift = max_shift
        self.tiles: List[TileInfo] = []
        
        # Initialize feature detector for position correction
        if enable_position_correction:
            self.feature_detector = cv2.SIFT_create(nfeatures=1000)
            self.matcher = cv2.BFMatcher()
    
    def parse_tile_configuration(self, config_path: str, image_directory: str = None) -> None:
        """Parse TileConfiguration.txt file"""
        if image_directory is None:
            image_directory = os.path.dirname(config_path)
        
        self.tiles = []
        
        with open(config_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#') or line.startswith('dim'):
                    continue
                
                if ';' in line:
                    parts = line.split(';')
                    if len(parts) >= 3:
                        filename = parts[0].strip()
                        position_str = parts[2].strip()
                        
                        # Extract coordinates
                        import re
                        coord_match = re.search(r'\(([-+]?\d*\.?\d+),\s*([-+]?\d*\.?\d+)\)', position_str)
                        if coord_match:
                            x = float(coord_match.group(1))
                            y = float(coord_match.group(2))
                            
                            full_path = os.path.join(image_directory, filename)
                            tile = TileInfo(filename=full_path, position=(x, y))
                            self.tiles.append(tile)
        
        logger.info(f"Parsed {len(self.tiles)} tiles from configuration")
    
    def load_images(self) -> None:
        """Load all images efficiently"""
        logger.info("Loading images...")
        start_time = time.time()
        
        for i, tile in enumerate(self.tiles):
            img = cv2.imread(tile.filename, cv2.IMREAD_GRAYSCALE)
            if img is None:
                logger.warning(f"Failed to load: {tile.filename}")
                continue
            
            tile.image = img
            tile.size = (img.shape[1], img.shape[0])
            
            if (i + 1) % 20 == 0:
                logger.info(f"Loaded {i + 1}/{len(self.tiles)} images")
        
        load_time = time.time() - start_time
        logger.info(f"Image loading completed in {load_time:.2f}s")
    
    def correct_positions_with_features(self) -> None:
        """Correct tile positions using feature matching"""
        if not self.enable_position_correction:
            # Copy original positions
            for tile in self.tiles:
                tile.corrected_position = tile.position
            return
        
        logger.info("Performing position correction using feature matching...")
        start_time = time.time()
        
        # Initialize corrected positions with original positions
        for tile in self.tiles:
            tile.corrected_position = tile.position
        
        corrections_made = 0
        
        # Find overlapping pairs and correct positions
        for i in range(len(self.tiles)):
            for j in range(i + 1, len(self.tiles)):
                tile1, tile2 = self.tiles[i], self.tiles[j]
                
                # Check if tiles are potentially overlapping
                if self._tiles_potentially_overlap(tile1, tile2):
                    correction = self._compute_position_correction(tile1, tile2)
                    if correction is not None:
                        # Apply correction to tile2
                        old_pos = tile2.corrected_position
                        new_pos = (old_pos[0] + correction[0], old_pos[1] + correction[1])
                        tile2.corrected_position = new_pos
                        corrections_made += 1
                        
                        logger.debug(f"Corrected {os.path.basename(tile2.filename)}: "
                                   f"{old_pos} -> {new_pos} (shift: {correction})")
        
        correction_time = time.time() - start_time
        logger.info(f"Position correction completed in {correction_time:.2f}s, "
                   f"made {corrections_made} corrections")
    
    def _tiles_potentially_overlap(self, tile1: TileInfo, tile2: TileInfo) -> bool:
        """Check if two tiles potentially overlap"""
        pos1, pos2 = tile1.corrected_position, tile2.corrected_position
        size1, size2 = tile1.size, tile2.size
        
        # Calculate bounding boxes
        x1_min, y1_min = pos1[0], pos1[1]
        x1_max, y1_max = pos1[0] + size1[0], pos1[1] + size1[1]
        
        x2_min, y2_min = pos2[0], pos2[1]
        x2_max, y2_max = pos2[0] + size2[0], pos2[1] + size2[1]
        
        # Check for overlap
        overlap_x = max(0, min(x1_max, x2_max) - max(x1_min, x2_min))
        overlap_y = max(0, min(y1_max, y2_max) - max(y1_min, y2_min))
        
        # Require at least 10% overlap
        min_overlap = min(size1[0] * size1[1], size2[0] * size2[1]) * 0.1
        return overlap_x * overlap_y > min_overlap
    
    def _compute_position_correction(self, tile1: TileInfo, tile2: TileInfo) -> Optional[Tuple[float, float]]:
        """Compute position correction using feature matching"""
        try:
            # Detect features
            kp1, des1 = self.feature_detector.detectAndCompute(tile1.image, None)
            kp2, des2 = self.feature_detector.detectAndCompute(tile2.image, None)
            
            if des1 is None or des2 is None or len(des1) < 10 or len(des2) < 10:
                return None
            
            # Match features
            matches = self.matcher.knnMatch(des1, des2, k=2)
            
            # Filter good matches using Lowe's ratio test
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < 0.7 * n.distance:
                        good_matches.append(m)
            
            if len(good_matches) < 10:
                return None
            
            # Extract matched points
            pts1 = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            pts2 = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            
            # Find homography
            H, mask = cv2.findHomography(pts2, pts1, cv2.RANSAC, 5.0)
            
            if H is None:
                return None
            
            # Extract translation from homography
            dx = H[0, 2]
            dy = H[1, 2]
            
            # Validate translation
            if abs(dx) > self.max_shift or abs(dy) > self.max_shift:
                return None
            
            # Calculate confidence based on inlier ratio
            inliers = np.sum(mask)
            confidence = inliers / len(good_matches)
            
            if confidence < self.correlation_threshold:
                return None
            
            return (dx, dy)
            
        except Exception as e:
            logger.debug(f"Feature matching failed: {e}")
            return None
    
    def create_optimized_canvas(self) -> Tuple[np.ndarray, np.ndarray, Tuple[float, float]]:
        """Create canvas with optimized memory layout"""
        # Calculate bounds using corrected positions
        min_x = min(tile.corrected_position[0] for tile in self.tiles)
        min_y = min(tile.corrected_position[1] for tile in self.tiles)
        max_x = max(tile.corrected_position[0] + tile.size[0] for tile in self.tiles)
        max_y = max(tile.corrected_position[1] + tile.size[1] for tile in self.tiles)
        
        canvas_width = int(np.ceil(max_x - min_x))
        canvas_height = int(np.ceil(max_y - min_y))
        offset = (min_x, min_y)
        
        logger.info(f"Canvas size: {canvas_width}x{canvas_height}, offset: {offset}")
        
        # Initialize canvas and weight map
        canvas = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        weights = np.zeros((canvas_height, canvas_width), dtype=np.float32)
        
        return canvas, weights, offset
    
    def optimized_linear_blending(self, canvas: np.ndarray, weights: np.ndarray, 
                                 img: np.ndarray, x_offset: int, y_offset: int) -> None:
        """Optimized linear blending using vectorized operations"""
        h, w = img.shape
        
        # Calculate valid region on canvas
        canvas_h, canvas_w = canvas.shape
        
        # Clip to canvas bounds
        start_y = max(0, y_offset)
        end_y = min(canvas_h, y_offset + h)
        start_x = max(0, x_offset)
        end_x = min(canvas_w, x_offset + w)
        
        if start_y >= end_y or start_x >= end_x:
            return
        
        # Calculate corresponding region in source image
        img_start_y = start_y - y_offset
        img_end_y = end_y - y_offset
        img_start_x = start_x - x_offset
        img_end_x = end_x - x_offset
        
        # Extract regions
        canvas_region = canvas[start_y:end_y, start_x:end_x]
        weight_region = weights[start_y:end_y, start_x:end_x]
        img_region = img[img_start_y:img_end_y, img_start_x:img_end_x].astype(np.float32)
        
        # Create distance-based weights (vectorized)
        region_h, region_w = img_region.shape
        y_coords, x_coords = np.ogrid[:region_h, :region_w]
        
        # Distance from edges
        dist_from_edges = np.minimum(
            np.minimum(x_coords + 1, region_w - x_coords),
            np.minimum(y_coords + 1, region_h - y_coords)
        ).astype(np.float32)
        
        # Normalize weights
        max_dist = np.max(dist_from_edges)
        if max_dist > 0:
            blend_weights = dist_from_edges / max_dist
        else:
            blend_weights = np.ones_like(dist_from_edges)
        
        # Apply blending (vectorized)
        mask = weight_region == 0  # First time pixels
        
        # For first-time pixels
        canvas_region[mask] = img_region[mask]
        weight_region[mask] = blend_weights[mask]
        
        # For overlapping pixels
        overlap_mask = ~mask
        if np.any(overlap_mask):
            total_weight = weight_region[overlap_mask] + blend_weights[overlap_mask]
            canvas_region[overlap_mask] = (
                (canvas_region[overlap_mask] * weight_region[overlap_mask] + 
                 img_region[overlap_mask] * blend_weights[overlap_mask]) / total_weight
            )
            weight_region[overlap_mask] = total_weight
    
    def optimized_max_fusion(self, canvas: np.ndarray, img: np.ndarray, 
                           x_offset: int, y_offset: int) -> None:
        """Optimized max fusion using vectorized operations"""
        h, w = img.shape
        canvas_h, canvas_w = canvas.shape
        
        # Calculate valid region
        start_y = max(0, y_offset)
        end_y = min(canvas_h, y_offset + h)
        start_x = max(0, x_offset)
        end_x = min(canvas_w, x_offset + w)
        
        if start_y >= end_y or start_x >= end_x:
            return
        
        # Extract regions
        img_start_y = start_y - y_offset
        img_end_y = end_y - y_offset
        img_start_x = start_x - x_offset
        img_end_x = end_x - x_offset
        
        canvas_region = canvas[start_y:end_y, start_x:end_x]
        img_region = img[img_start_y:img_end_y, img_start_x:img_end_x].astype(np.float32)
        
        # Vectorized max operation
        np.maximum(canvas_region, img_region, out=canvas_region)
    
    def stitch_images(self) -> np.ndarray:
        """Main stitching function with optimizations"""
        logger.info(f"Starting optimized stitching of {len(self.tiles)} tiles")
        start_time = time.time()
        
        # Load images
        self.load_images()
        
        # Correct positions
        self.correct_positions_with_features()
        
        # Create canvas
        canvas, weights, offset = self.create_optimized_canvas()
        
        # Place tiles with optimized fusion
        logger.info("Performing image fusion...")
        fusion_start = time.time()
        
        for i, tile in enumerate(self.tiles):
            x_offset = int(tile.corrected_position[0] - offset[0])
            y_offset = int(tile.corrected_position[1] - offset[1])
            
            if self.fusion_method == "linear_blending":
                self.optimized_linear_blending(canvas, weights, tile.image, x_offset, y_offset)
            elif self.fusion_method == "max":
                self.optimized_max_fusion(canvas, tile.image, x_offset, y_offset)
            
            if (i + 1) % 20 == 0:
                logger.info(f"Fused {i + 1}/{len(self.tiles)} tiles")
        
        fusion_time = time.time() - fusion_start
        total_time = time.time() - start_time
        
        logger.info(f"Fusion completed in {fusion_time:.2f}s")
        logger.info(f"Total stitching time: {total_time:.2f}s")
        
        # Convert to uint8
        result = np.clip(canvas, 0, 255).astype(np.uint8)
        return result


def main():
    """Command line interface for improved stitching"""
    parser = argparse.ArgumentParser(description="Improved ImageJ-compatible Image Stitching")
    parser.add_argument("config_file", help="Path to TileConfiguration.txt file")
    parser.add_argument("-o", "--output", default="improved_result.jpg", help="Output filename")
    parser.add_argument("-m", "--method", default="linear_blending", 
                       choices=["linear_blending", "max"], help="Fusion method")
    parser.add_argument("--no-correction", action="store_true", 
                       help="Disable position correction")
    parser.add_argument("--correlation-threshold", type=float, default=0.3,
                       help="Correlation threshold for position correction")
    parser.add_argument("--max-shift", type=int, default=200,
                       help="Maximum allowed position shift")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Create improved stitcher
        stitcher = ImprovedStitcher(
            fusion_method=args.method,
            enable_position_correction=not args.no_correction,
            correlation_threshold=args.correlation_threshold,
            max_shift=args.max_shift
        )
        
        # Parse configuration
        stitcher.parse_tile_configuration(args.config_file)
        
        # Perform stitching
        result = stitcher.stitch_images()
        
        # Save result
        cv2.imwrite(args.output, result)
        logger.info(f"Improved stitching result saved: {args.output}")
        logger.info(f"Result size: {result.shape[1]}x{result.shape[0]} pixels")
        
        return 0
        
    except Exception as e:
        logger.error(f"Stitching failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
